<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-邮箱登录</Title>
    </Head>
    <!-- 标题 -->
    <h1>登&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;录</h1>
    <!-- 账号输入框 -->
    <div class="iinput email">
      <IInput
        placeholder="请输入邮箱"
        container-class="iinput-container"
        v-model="form.email"
        @keydown.enter="login"
      />
    </div>
    <!-- 密码输入框 -->
    <div class="iinput password">
      <IInput
        placeholder="请输入密码"
        type="password"
        container-class="iinput-container"
        v-model="form.password"
        @keydown.enter="login"
      />
    </div>
    <!-- 验证码输入框 -->
    <div class="iinput captcha">
      <IInput
        placeholder="请输入验证码"
        container-class="iinput-container"
        v-model="form.code"
        @keydown.enter="login"
      />
      <div class="captcha-img">
        <captcha ref="captchaRef" />
      </div>
    </div>
    <!-- 忘记密码和注册账户页面跳转按钮 -->
    <div class="link">
      <NuxtLink to="/auth/retrieve/email">忘记密码</NuxtLink>
      <NuxtLink to="/auth/register/email">注册账户</NuxtLink>
    </div>
    <!-- 分割线加文字 -->
    <div class="divider">
      <div class="line"></div>
      <span class="divider-text">其他方式登录</span>
      <div class="line"></div>
    </div>
    <!-- 其他登陆方式的图标 -->
    <div class="login-methods">
      <NuxtLink to="/auth/login/phone"
        ><div class="icon">
          <div class="phone">
            <IconSvg name="phone" size="1.5rem" color="#fff" />
          </div>
          <span>手机</span>
        </div></NuxtLink
      >
      <NuxtLink to="/auth/login/wechat">
        <div class="icon">
          <div class="wechat">
            <IconSvg name="wechat" size="1.5rem" color="#fff" />
          </div>
          <span>微信</span>
        </div>
      </NuxtLink>
    </div>
    <RippleButton class="login-button" @click.{enter}="login">
      登录
    </RippleButton>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'auth',
  keepalive: true,
});

const captchaRef = ref<CaptchaExpose | null>(null);
const router = useRouter();

// 表单数据
const form = ref({
  email: '<EMAIL>',
  password: '12345678910aA@',
  code: '',
});

// 登录
const login = async () => {
  // 表单验证
  if (!form.value.email) {
    return useMessage({
      name: '邮箱未输入',
      description: '邮箱必须输入',
      type: 'error',
    });
  } else {
    const emailReg =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!emailReg.test(form.value.email)) {
      return useMessage({
        name: '邮箱格式错误',
        description: '邮箱格式错误',
        type: 'error',
      });
    }
  }
  if (!form.value.password) {
    return useMessage({
      name: '密码未输入',
      description: '密码必须输入',
      type: 'error',
    });
  }
  if (!form.value.code) {
    return useMessage({
      name: '验证码未输入',
      description: '验证码必须输入',
      type: 'error',
    });
  }

  try {
    // 登录
    await useApi().loginByEmailOrPhone({
      email: form.value.email,
      password: form.value.password,
      code: form.value.code,
    });

    await useApi().getMyInfo();

    router.replace('/mine');
  } catch (err) {
    captchaRef.value?.refresh();
    throw err;
  }
};
</script>

<style lang="scss" scoped>
.page-container {
  min-width: 14rem;
  // 标题的样式
  > h1 {
    text-align: center;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
  }

  // 输入框的样式
  > .iinput {
    margin-bottom: 0.5rem;

    &.captcha {
      display: flex;

      > .captcha-img {
        margin-left: 0.5rem;
      }
    }

    .iinput-container {
      color: var(--text-primary);
    }
  }

  // 注册账户和忘记密码页面跳转按钮的样式
  > .link {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    text-decoration: underline;
    text-decoration-color: var(--text-link-underline);
    color: var(--text-link);
  }

  /* 分割线样式 */
  .divider {
    display: flex;
    align-items: center;
    margin: 0.5rem 0;

    > .line {
      flex: 1;
      height: 1px;
      background: rgba(255, 255, 255, 0.3); /* 半透明分割线 */
    }

    > .divider-text {
      padding: 0 1rem;
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.9em;
    }
  }

  // 其他登陆方式区域的样式
  > .login-methods {
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-evenly;

    .icon {
      display: flex;
      justify-content: center;
      align-items: center;

      > div {
        padding: 0.3rem;
        border-radius: 50%;

        &.phone {
          background-color: #4228c4;
        }

        &.wechat {
          background-color: #28c445;
        }
      }

      > span {
        margin-left: 0.5rem;
      }
    }
  }

  // 登录按钮的样式
  > .login-button {
    margin: 0 auto;
    width: 8rem;
    background-color: var(--button-primary);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-primary-hover);
    }
  }
}
</style>
