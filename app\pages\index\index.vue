<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-推荐</Title>
    </Head>
    <!-- 图片推荐区域 -->
    <RikkaBanner
      title="图片热度推荐"
      subtitle="发现最受欢迎的视觉盛宴"
      theme="magic"
    />
    <!-- 图片推荐瀑布流展示组件 -->
    <RikkaImagesWaterfallGallery
      :items="imagesArray"
      :columns="0"
      :card-width="300"
      :gap="20"
      :loading="imagesLoading"
      :has-more="hasMoreImages"
      @load-more="handleLoadMoreImages"
      :height-limit="10"
    />

    <!-- 帖子推荐区域 -->
    <RikkaBanner
      title="帖子热度推荐"
      subtitle="发现最受欢迎的视觉盛宴"
      theme="gothic"
    />

    <RikkaPostsWaterfallGallery
      :posts="postsArray"
      :columns="0"
      :card-width="300"
      :gap="20"
      :loading="postsLoading"
      :has-more="hasMorePosts"
      @load-more="handleLoadMorePosts"
      :height-limit="10"
    />

    <!-- 用户推荐区域 -->
    <RikkaBanner
      title="用户热度推荐"
      subtitle="发现最受欢迎的视觉盛宴"
      theme="simple"
    />

    <RikkaUsersGrid
      :users="usersArray"
      :card-width="300"
      :gap="20"
      :loading="usersLoading"
      :has-more="hasMoreUsers"
      @load-more="handleLoadMoreUsers"
      :max-rows="1"
    />
  </div>
</template>

<script lang="ts" setup>
// 设置页面元信息，使用首页布局
definePageMeta({
  layout: 'home',
  keepalive: true,
});

/** 图片推荐模块 */
// 响应式图片数组
const imagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
// 加载状态
const imagesLoading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMoreImages = computed(
  () => imagesPaginated.value.page < imagesPaginated.value.totalPage
);
// 分页信息
const imagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});

/**
 * 获取图片列表数据
 * @param query 可选查询参数
 */
const getPhotosList = async (query?: PhotosListQuery) => {
  try {
    const { list, ...res } = await useApi().getPhotosList(query);
    imagesArray.value.push(...list);
    imagesPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

/**
 * 处理加载更多数据的逻辑
 */
const handleLoadMoreImages = async () => {
  if (imagesLoading.value || !hasMoreImages.value) {
    return;
  }
  imagesLoading.value = true;

  await getPhotosList({
    page: imagesPaginated.value.page + 1,
    pageSize: 20,
    sortField: 'downloadCount',
    sortOrder: 'desc',
  });
  imagesLoading.value = false;
};

/** 帖子推荐模块 */
// 响应式帖子数组
const postsArray = ref<PostsWaterfallGalleryItem[]>([]);
// 加载状态
const postsLoading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMorePosts = computed(
  () => postsPaginated.value.page < postsPaginated.value.totalPage
);
// 分页信息
const postsPaginated = ref<Paginated<PostsSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'likeCount',
  sortOrder: 'desc',
});

/**
 * 获取帖子列表数据
 * @param query 可选查询参数
 */
const getPostsList = async (query?: PostsListQuery) => {
  try {
    // 获取帖子列表数据
    const { list, ...res } = await useApi().getPostList(query);

    // 处理每条帖子的关联图片信息
    const newData = await Promise.all(
      list.map(async (v) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;

        return {
          ...rest,
          photos, // 保留原有 photos 字段
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );

    // 如果是第一页，清空原有数据
    if (!query || !query.page || query.page === 1) {
      postsArray.value = [];
    }

    // 更新帖子数组和分页信息
    postsArray.value.push(...newData);
    postsPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

/**
 * 处理加载更多数据的逻辑
 */
const handleLoadMorePosts = async () => {
  // 如果正在加载或没有更多数据，则直接返回
  if (postsLoading.value || !hasMorePosts.value) {
    return;
  }
  postsLoading.value = true;

  // 加载下一页数据
  await getPostsList({
    page: postsPaginated.value.page + 1,
    pageSize: 20,
    sortField: 'likeCount',
  });
  postsLoading.value = false;
};

/** 用户推荐模块 */
const usersArray = ref<UsersList[]>([]);
const usersLoading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMoreUsers = computed(
  () => usersPaginated.value.page < usersPaginated.value.totalPage
);
// 分页信息
const usersPaginated = ref<Paginated<UserSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'totalDownloads',
  sortOrder: 'desc',
});
const getUserList = async (query?: UserListQuery) => {
  try {
    const { list, ...res } = await useApi().getUserList(query);
    usersArray.value.push(...list);
    usersPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

const handleLoadMoreUsers = async () => {
  if (usersLoading.value || !hasMoreUsers.value) {
    return;
  }
  usersLoading.value = true;

  await getUserList({
    page: usersPaginated.value.page + 1,
    pageSize: 20,
    sortField: 'totalDownloads',
  });
  usersLoading.value = false;
};
</script>

<style lang="scss" scoped>
/* 页面容器样式 */
.page-container {
  height: 100%;
  flex-direction: column;
  overflow: auto;
}
</style>
