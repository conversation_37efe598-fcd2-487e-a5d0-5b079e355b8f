<template>
  <div class="post-details">
    <Head>
      <Title>{{ postDetail?.title || '帖子详情' }}</Title>
      <Meta
        name="keywords"
        :content="
          postDetail?.tags.join(',') +
          ',' +
          postDetail?.category +
          ',' +
          ',次元回廊,Dimensional Corridor,二次元,图片分享,交流平台,交友,壁纸'
        "
      />
    </Head>
    <!-- 主内容区 -->
    <div class="content-container">
      <!-- 左侧内容展示区 -->
      <div class="post-display">
        <div class="post-content" v-if="postDetail">
          <!-- 帖子标题 -->
          <h1 class="post-title">{{ postDetail.title }}</h1>

          <!-- 帖子正文 -->
          <div class="post-body">{{ postDetail.content }}</div>

          <!-- 帖子图片九宫格 - 只在有图片时显示 -->
          <div
            class="post-images"
            v-if="postDetail.photos && postDetail.photos.length > 0"
          >
            <div class="images-grid">
              <div
                v-for="(photo, index) in postDetail.photos"
                :key="index"
                class="image-item"
                @click="
                  $router.push({
                    path: '/photos/details',
                    query: { id: photo.id },
                  })
                "
              >
                <img :src="photo.url + '?width=800'" :alt="photo.filename" />
              </div>
            </div>
          </div>

          <!-- 帖子操作按钮 -->
          <div class="post-actions">
            <div class="action-button" @click="toggleFavorite">
              <IconSvg
                name="favorite"
                :size="20"
                :color="postDetail?.isFavorited ? '#FFD700' : '#FFFFFF'"
              />
            </div>
            <div class="action-button" @click="toggleLike">
              <IconSvg
                name="like"
                :size="20"
                :color="postDetail?.isLiked ? '#FF5252' : '#FFFFFF'"
              />
            </div>
          </div>
        </div>
        <div v-else class="content-placeholder">加载中...</div>
      </div>

      <!-- 右侧信息区 -->
      <div class="post-info-simple">
        <!-- 基础信息分组 -->
        <div class="info-group" data-title="基本信息" v-if="postDetail">
          <div class="info-row">
            <span v-if="postDetail?.photos?.length">
              <IconSvg name="image" :size="16" color="var(--text-secondary)" />
              {{ postDetail.photos.length }} 张图片
            </span>
          </div>
        </div>

        <!-- 统计信息分组 -->
        <div class="info-group" data-title="统计数据">
          <div class="info-row">
            <span>
              <IconSvg
                name="favorite"
                :size="16"
                color="var(--text-secondary)"
              />
              {{ postDetail?.favoriteCount || 0 }}
            </span>
            <span>
              <IconSvg name="like" :size="16" color="var(--text-secondary)" />
              {{ postDetail?.likeCount || 0 }}
            </span>
          </div>
        </div>

        <!-- 作者信息分组 -->
        <div
          class="info-group"
          data-title="作者信息"
          @click="navigateToUser(postDetail?.user.uid || '')"
        >
          <div class="info-row user-row" v-if="postDetail?.user">
            <img
              :src="postDetail.user.avatar"
              :alt="postDetail.user.uid"
              class="user-avatar"
            />
            <span class="username">{{ postDetail.user.nickname }}</span>
          </div>
        </div>

        <!-- 标签分组 -->
        <div
          class="info-group tags-group"
          data-title="标签"
          v-if="postDetail?.tags?.length"
        >
          <div class="info-row tags-row">
            <span
              class="tag"
              v-for="(tag, index) in postDetail.tags"
              :key="index"
              @click="searchByTag(tag)"
            >
              {{ tag }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 相关推荐部分 -->
    <div class="related-section" v-if="postDetail">
      <h3>相关推荐</h3>
      <div class="related-container">
        <RikkaPostsWaterfallGallery
          :posts="postsArray.filter((item) => item.id !== postDetail?.id)"
          :columns="0"
          :card-width="250"
          :gap="20"
          :loading="postsLoading"
          :has-more="hasMorePosts"
          @load-more="handleLoadMorePosts"
          :height-limit="500"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'detail',
  keepalive: true,
});

const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const id = route.query.id as string;
// 帖子详情
const postDetail = ref<GetOtherPostsInfo>();
// 加载状态
const loading = ref(true);
// 错误状态
const error = ref(false);

// 获取帖子详情
const getPostDetail = async (id: string) => {
  if (!id) {
    error.value = true;
    loading.value = false;
    useMessage({
      name: '参数错误',
      description: '缺少帖子ID',
      type: 'error',
    });
    return;
  }

  loading.value = true;
  error.value = false;

  try {
    const data = await useApi().getOtherPostInfo(id);
    // 确保photos字段存在且是数组
    if (!data.photos) {
      data.photos = [];
    }
    // 确保tags字段存在且是数组
    if (!data.tags) {
      data.tags = [];
    }
    postDetail.value = data;
  } catch (err) {
    console.error('帖子详情', '获取帖子详情失败', err);
    error.value = true;
    useMessage({
      name: '加载失败',
      description: '无法获取帖子详情',
      type: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// 导航到用户页面
const navigateToUser = (uid: string) => {
  if (!uid) return;
  if (uid === authStore.authStore?.uid) {
    return router.push('/mine');
  }
  router.push({ path: '/user', query: { uid } });
};

// 按标签搜索
const searchByTag = (tag: string) => {
  router.push({ path: '/search/posts', query: { key: tag } });
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  return dayjs(dateString).format('YYYY年MM月DD日');
};

// 切换收藏状态
const toggleFavorite = async () => {
  if (!postDetail.value) return;

  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '收藏功能需要登录后使用',
      type: 'info',
    });
    router.push('/auth/login');
    return;
  }

  try {
    await useApi().toggleFavoritePost(postDetail.value.id);
    // 更新本地状态
    postDetail.value.isFavorited = !postDetail.value.isFavorited;
    postDetail.value.favoriteCount += postDetail.value.isFavorited ? 1 : -1;

    useMessage({
      name: postDetail.value.isFavorited ? '收藏成功' : '取消收藏',
      description: postDetail.value.isFavorited
        ? '已添加到收藏'
        : '已从收藏中移除',
      type: 'success',
    });
  } catch (error) {
    useMessage({
      name: '操作失败',
      description: '请稍后再试',
      type: 'error',
    });
  }
};

// 切换点赞状态
const toggleLike = async () => {
  if (!postDetail.value) return;

  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '点赞功能需要登录后使用',
      type: 'info',
    });
    router.push('/auth/login');
    return;
  }

  try {
    await useApi().toggleLikePost(postDetail.value.id);
    // 更新本地状态
    postDetail.value.isLiked = !postDetail.value.isLiked;
    postDetail.value.likeCount += postDetail.value.isLiked ? 1 : -1;

    useMessage({
      name: postDetail.value.isLiked ? '点赞成功' : '取消点赞',
      description: '',
      type: 'success',
    });
  } catch (error) {
    useMessage({
      name: '操作失败',
      description: '请稍后再试',
      type: 'error',
    });
  }
};

/** 帖子推荐模块 */
// 响应式帖子数组
const postsArray = ref<PostsWaterfallGalleryItem[]>([]);
// 加载状态
const postsLoading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMorePosts = computed(
  () => postsPaginated.value.page < postsPaginated.value.totalPage
);
// 分页信息
const postsPaginated = ref<Paginated<PostsSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'likeCount',
  sortOrder: 'asc',
});

/**
 * 获取帖子列表数据
 * @param query 可选查询参数
 */
const getPostsList = async (query?: PostsListQuery) => {
  try {
    // 获取帖子列表数据
    const { list, ...res } = await useApi().getPostList(query);

    // 处理每条帖子的关联图片信息
    const newData = await Promise.all(
      list.map(async (v) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;

        return {
          ...rest,
          photos, // 保留原有 photos 字段
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );

    // 如果是第一页，清空原有数据
    if (!query || !query.page || query.page === 1) {
      postsArray.value = [];
    }

    // 更新帖子数组和分页信息
    postsArray.value.push(...newData);
    postsPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

/**
 * 处理加载更多数据的逻辑
 */
const handleLoadMorePosts = async () => {
  // 如果正在加载或没有更多数据，则直接返回
  if (postsLoading.value || !hasMorePosts.value) {
    return;
  }
  postsLoading.value = true;

  // 加载下一页数据
  await getPostsList({
    page: postsPaginated.value.page + 1,
    pageSize: 20,
    tags: ['', '', ...(postDetail.value?.tags || [])],
  });
  postsLoading.value = false;
};

// 监听路由变化，当 id 参数改变时重新加载数据
watch(
  () => route.query.id,
  async (newId) => {
    if (newId && typeof newId === 'string') {
      // 重置滚动位置到顶部
      if (import.meta.client) {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }

      // 加载新的帖子详情
      await getPostDetail(newId);

      // 重置相关推荐数据
      postsArray.value = [];
      postsPaginated.value = {
        page: 0,
        pageSize: 10,
        totalCount: 0,
        totalPage: 1,
        sortField: 'likeCount',
        sortOrder: 'asc',
      };

      // 加载推荐帖子
      if (postDetail.value) {
        await getPostsList({
          page: 1,
          pageSize: 20,
          tags: ['', '', ...postDetail.value.tags],
        });
      }
    }
  },
  { immediate: true } // 立即执行一次，相当于初始化加载
);
</script>

<style lang="scss" scoped>
.post-details {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  position: relative;
  padding: 1rem;

  .content-container {
    display: flex;
    gap: 1rem;
  }

  .post-display {
    flex: 3;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(
      135deg,
      var(--background-elevated) 0%,
      var(--background-floating) 100%
    );
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow:
      0 0.5rem 2rem rgba(0, 0, 0, 0.15),
      0 0 0 0.1rem rgba(255, 255, 255, 0.05),
      var(--shadow-neon-primary);
    min-height: 37.5rem;
    border-left: 0.3rem solid var(--interactive-primary);
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.15rem;
      background: linear-gradient(
        90deg,
        transparent 0%,
        var(--interactive-primary) 50%,
        transparent 100%
      );
      z-index: 1;
    }

    .post-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;
      width: 100%;

      .post-title {
        font-size: 2.2rem;
        font-weight: 700;
        color: var(--text-primary);
        text-align: center;
        margin-bottom: 1.5rem;
        position: relative;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 1rem;
        border: 0.05rem solid rgba(255, 255, 255, 0.1);
      }

      .post-body {
        font-size: 1.1rem;
        line-height: 1.7;
        color: var(--text-secondary);
        white-space: pre-wrap;
        width: 100%;
        text-align: left;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 1rem;
        border: 0.05rem solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
      }

      .post-images {
        margin-top: 2rem;
        width: 100%;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 1rem;
        border: 0.05rem solid rgba(255, 255, 255, 0.1);

        .images-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          grid-gap: 1rem;

          .image-item {
            aspect-ratio: 1;
            overflow: hidden;
            border-radius: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow:
              0 0.25rem 1rem rgba(0, 0, 0, 0.15),
              0 0 0 0.05rem rgba(255, 255, 255, 0.1);

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }
    }

    .post-actions {
      position: absolute;
      bottom: 2.5rem;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 2rem;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(20px);
      padding: 1rem 2rem;
      border-radius: 3rem;
      box-shadow:
        0 0.5rem 2rem rgba(0, 0, 0, 0.4),
        0 0 0 0.1rem rgba(255, 255, 255, 0.1),
        var(--shadow-neon-primary);
      z-index: 10;
      border: 0.1rem solid rgba(255, 255, 255, 0.15);

      .action-button {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 0.1rem solid rgba(255, 255, 255, 0.2);
        position: relative;

        &::before {
          content: '';
          position: absolute;
          inset: -0.1rem;
          border-radius: 50%;
          background: linear-gradient(
            45deg,
            transparent 0%,
            rgba(255, 255, 255, 0.2) 50%,
            transparent 100%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: scale(1.15);
          background: rgba(255, 255, 255, 0.25);
          box-shadow:
            0 0 2rem rgba(255, 255, 255, 0.4),
            0 0.5rem 1rem rgba(0, 0, 0, 0.3);

          &::before {
            opacity: 1;
          }
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }

    .content-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 1.2rem;
      color: var(--text-secondary);
    }
  }

  .post-info-simple {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    gap: 1rem;
    width: 300px;
    min-width: 300px;

    .info-group {
      background: linear-gradient(
        135deg,
        var(--background-elevated) 0%,
        var(--background-floating) 100%
      );
      border-radius: 1.2rem;
      box-shadow:
        0 0.25rem 1rem rgba(0, 0, 0, 0.12),
        0 0 0 0.1rem rgba(255, 255, 255, 0.05);
      padding: 1.5rem;
      margin-bottom: 1rem;
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      position: relative;
      border-left: 0.25rem solid var(--interactive-primary);
      transition: all 0.3s ease;

      &::before {
        content: attr(data-title);
        position: absolute;
        top: -0.75rem;
        left: 1.5rem;
        background: linear-gradient(
          135deg,
          var(--background-elevated) 0%,
          var(--background-floating) 100%
        );
        padding: 0.25rem 0.75rem;
        font-size: 0.85rem;
        color: var(--interactive-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        border: 0.05rem solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 0.1rem;
        background: linear-gradient(
          90deg,
          transparent 0%,
          var(--interactive-primary) 50%,
          transparent 100%
        );
        border-radius: 1.2rem 1.2rem 0 0;
      }
    }

    .info-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      gap: 1.2rem;
      font-size: 0.97rem;
      color: var(--text-secondary);
      padding: 0.3rem 0;

      span {
        display: flex;
        align-items: center;
        gap: 0.3rem;
      }
    }

    .user-row {
      gap: 0.8rem;
      cursor: pointer;
      transition: transform 0.2s ease;
      padding: 0.5rem;
      border-radius: 0.5rem;

      &:hover {
        background: var(--background-floating);
        transform: translateY(-2px);
      }

      .user-avatar {
        width: 2.2rem;
        height: 2.2rem;
        border-radius: 50%;
        object-fit: cover;
        border: 0.15rem solid var(--border-focus-ring);
      }

      .username {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 1rem;
      }
    }

    .tags-group {
      .tags-row {
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: flex-start;

        .tag {
          background: var(--interactive-primary-translucent);
          color: var(--text-primary);
          padding: 0.3rem 0.8rem;
          border-radius: 1rem;
          font-size: 0.85rem;
          cursor: pointer;
          transition: all 0.2s ease;
          border: 1px solid transparent;

          &:hover {
            background: var(--interactive-primary);
            color: var(--text-on-primary);
            transform: translateY(-1px);
            box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }

  .related-section {
    h3 {
      margin-bottom: 1.2rem;
      font-size: 1.4rem;
      color: var(--text-primary);
      font-weight: 600;
    }
    padding-bottom: 2rem;
  }
}

.related-section {
  background: linear-gradient(
    135deg,
    var(--background-elevated) 0%,
    var(--background-floating) 100%
  );
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow:
    0 0.5rem 2rem rgba(0, 0, 0, 0.15),
    0 0 0 0.1rem rgba(255, 255, 255, 0.05);
  border-left: 0.3rem solid var(--interactive-primary);
  position: relative;
  margin-bottom: 2rem;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.15rem;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--interactive-primary) 50%,
      transparent 100%
    );
    border-radius: 1.5rem 1.5rem 0 0;
  }

  h3 {
    margin-bottom: 2rem;
    font-size: 1.5rem;
    color: var(--text-primary);
    font-weight: 600;
    position: relative;
    padding-left: 2rem;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 0.4rem;
      height: 1.5rem;
      background: linear-gradient(
        180deg,
        var(--interactive-primary) 0%,
        var(--interactive-primary-hover) 100%
      );
      border-radius: 0.2rem;
      box-shadow: 0 0 1rem var(--interactive-primary-translucent);
    }

    &::after {
      content: '';
      position: absolute;
      left: 0.6rem;
      top: 50%;
      transform: translateY(-50%);
      width: 0.15rem;
      height: 1rem;
      background: rgba(255, 255, 255, 0.4);
      border-radius: 0.075rem;
    }
  }
}

@media (max-width: 768px) {
  .content-container {
    flex-direction: column;
  }

  .post-info-simple {
    width: 100%;
    min-width: 0;
  }
}
</style>

