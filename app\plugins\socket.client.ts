import { io, type Socket } from 'socket.io-client';
import { reactive } from '#imports'; // 显式引入以避免 ESLint 报错

export default defineNuxtPlugin((nuxtApp) => {
  const authStore = useAuthStore();

  if (import.meta.client && authStore.isAuthenticated) {
    const config = useRuntimeConfig();
    const { $logger } = useNuxtApp();

    const socket: Socket = io(config.public.socketUrl, {
      auth: {
        authorization: `Bearer ${authStore.getToken()}`,
      },
      autoConnect: false,
      transports: ['websocket'],
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 2000,
    });

    // 响应式状态
    const state = reactive({
      isConnected: socket.connected,
      transport: socket.io?.engine?.transport?.name || 'N/A',
      error: null as Error | null,
    });

    // 连接成功
    const onConnect = () => {
      state.isConnected = true;
      state.transport = socket.io.engine.transport.name;
      $logger.info('Socket 连接成功');
    };

    // 连接断开
    const onDisconnect = () => {
      state.isConnected = false;
      state.transport = 'N/A';
      $logger.warn('Socket 连接断开');
    };

    // 连接错误
    const onError = (error: Error) => {
      state.error = error;
      $logger.error('Socket 连接错误:');
    };

    // 重连失败
    const onReconnectFailed = () => {
      $logger.error('Socket 重连失败');
    };

    // 绑定事件监听器
    socket.on('connect', onConnect);
    socket.on('disconnect', onDisconnect);
    socket.on('connect_error', onError);
    socket.on('reconnect_failed', onReconnectFailed);

    // 注入到 Nuxt 上下文
    nuxtApp.provide('socket', {
      socket,
      state,
    });
  }
});

