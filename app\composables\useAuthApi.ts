/**
 * 用户登录接口
 * @param body - 登录参数对象，包含手机号/邮箱、密码和验证码
 * @returns 返回Promise，解析后得到token字符串
 */
const loginByEmailOrPhone = async (body: ApiloginByEmailOrPhoneParams) => {
  const authStore = useAuthStore(); // 获取认证状态管理实例

  try {
    // 发送登录请求
    const { data: token } = await useRequest<ApiResponse<string>>(
      '/auth/login-password',
      {
        method: 'POST',
        body,
        server: false,
        token: false, // 不携带token
      }
    );

    authStore.setToken(token); // 存储token到状态管理

    // 显示登录成功消息
    useMessage({
      name: '登录成功',
      description: '欢迎回来',
      type: 'success',
    });
    useSocket().socket.connect();

    return token; // 返回token
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 用户注册接口
 * @param body - 注册参数对象，包含手机号/邮箱、密码和验证码
 * @returns 返回Promise，解析后得到注册结果
 */
const registerByEmailOrPhone = async (
  body: ApiregisterByEmailOrPhoneParams
) => {
  try {
    // 发送注册请求
    const data = await useRequest('/users', {
      method: 'POST',
      body,
      token: false, // 不携带token
    });

    // 显示注册成功消息
    useMessage({
      name: '注册成功',
      description: '欢迎加入',
      type: 'info',
    });

    return data; // 返回注册结果
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 重置密码接口
 * @param body - 重置密码参数对象，包含手机号/邮箱、新密码和验证码
 * @returns 无返回值
 */
const retrievePassword = async (body: ApiRetrievePasswordParams) => {
  try {
    // 发送重置密码请求
    await useRequest('/users/reset-password', {
      method: 'PUT',
      body,
      token: false, // 不携带token
    });

    // 显示重置成功消息
    useMessage({
      name: '重置成功',
      description: '快去登陆吧！',
      type: 'info',
    });

    return;
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 用户登出接口
 * @returns 无返回值
 */
const logout = async () => {
  const authStore = useAuthStore(); // 获取认证状态管理实例

  try {
    // 发送登出请求
    await useRequest('/auth/logout', {
      method: 'POST',
      token: false, // 不携带token
    });

    authStore.clear(); // 清除认证状态

    // 显示登出成功消息
    useMessage({
      name: '退出成功',
      description: '下次再会',
      type: 'info',
    });

    useSocket().socket.disconnect();

    return;
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 导出认证相关API
 * @returns 返回包含所有认证API方法的对象
 */
export const useAuthApi = () => {
  return {
    /** 用户登录方法，添加防抖(200ms) */
    loginByEmailOrPhone: useDebounceFn(loginByEmailOrPhone, 200),
    /** 用户注册方法，添加防抖(200ms) */
    registerByEmailOrPhone: useDebounceFn(registerByEmailOrPhone, 200),
    /** 重置密码方法，添加防抖(200ms) */
    retrievePassword: useDebounceFn(retrievePassword, 200),
    /** 用户登出方法，添加防抖(200ms) */
    logout: useDebounceFn(logout, 200),
  };
};

