@import './base.css';
@import './tailwind.css';
@import './font.css';
@import './theme-default.css';

#nprogress .bar,
#nprogress .peg {
  background-color: var(--interactive-primary) !important;
}

html {
  /* 设置字体大小 */
  font-size: clamp(12px, 2vmin, 20px);
  /* 设置字体 */
  font-family: 'DouyinSansBold', 'Microsoft YaHei' !important;
}

/* 全局滚动条样式 */

/* Webkit浏览器 (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-left: 0.1rem solid transparent; /* 防止轨道边缘闪烁 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
}

/* 悬停状态 */
::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}
