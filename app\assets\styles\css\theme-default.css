:root[class='default-mode'] {
  /* ================= 核心颜色体系 ================= */
  /* 主交互色（邪王真眼紫） */
  --interactive-primary: #8f33ff;
  --interactive-primary-hover: #ab66ff;
  --interactive-primary-active: #7300ff;
  --interactive-primary-translucent: #8f33ff26;

  /* 危险操作色（漆黑烈焰红）*/
  --interactive-danger: #d20f39;
  --interactive-danger-glow: #ff335f;

  /* 中性过渡色（圣调理人银）*/
  --neutral-border: #d5dbe2;
  --neutral-divider: #b7c2cd66; /* 40%透明度 */
  --neutral-disabled: #b7c2cd4d; /* 30%透明度 */
  --neutral-hover: #b7c2cd; /* 鼠标悬停 */

  /* ================= 空间层级体系 ================= */
  --background-base: #181825; /* 虚空背景 */
  --background-surface: #1f1d2f; /* 第一现实层 */
  --background-elevated: #29273f; /* 次元叠加层 */
  --background-floating: #34314e; /* 悬浮元素层 */
  /* 渐变色背景 */
  --background-gradient-1: linear-gradient(
    to bottom,
    var(--background-elevated),
    var(--background-floating)
  );
  /* 透明背景遮罩层 */
  --background-overlay: rgba(0, 0, 0, 0.5);

  /* ================= 文字视觉体系 ================= */
  --text-primary: #f2f2f2; /* 主文本（虚空之白） */
  --text-secondary: #bcbcc2; /* 次级文本（现世之灰） */
  --text-accent: #8f33ff; /* 强调文本 */
  --text-link: #66d9ff; /* 超链接（契约之蓝） */
  --text-link-underline: #66d9ff4d; /* 链接下划线 */

  /* ================= 按钮视觉体系 ================= */
  --button-primary: var(--interactive-primary); /* 主按钮 */
  --button-primary-hover: var(--interactive-primary-hover); /* 主按钮悬停 */
  --button-primary-active: var(--interactive-primary-active); /* 主按钮激活 */
  --button-primary-disabled: var(--neutral-disabled); /* 主按钮禁用 */
  --button-secondary: var(--interactive-primary); /* 次按钮 */
  --button-cancel: var(--neutral-divider); /* 取消按钮悬停 */
  --button-cancel-hover: var(--neutral-disabled); /* 取消按钮激活 */

  /* ================= 阴影特效体系 ================= */
  --shadow-dimension-sm: 0 0.1rem 0.2rem rgba(23, 18, 37, 0.25); /* 小型投影 */
  --shadow-dimension-md: 0 0.4rem 1.2rem rgba(31, 29, 47, 0.3); /* 中型投影 */
  --shadow-neon-primary: 0 0 0.8rem var(--interactive-primary-translucent); /* 邪王光晕 */
  --shadow-neon-danger: 0 0 1.2rem #ff335f33; /* 烈焰辉光 */
  --shadow-Dimension:
    0 0 20px rgba(255, 215, 0, 0.5), inset 0 0 15px rgba(106, 48, 147, 0.8); /* 紫金辉光 */
  /* 纯金光晕 */
  --shadow-neon-glow: 0 0 0.6rem rgba(255, 215, 0, 0.7);

  /* ================= 界面装饰体系 ================= */
  --border-radius-sm: 0.5rem; /* 小型圆角 */
  --border-radius-md: 1rem; /* 中型圆角 */
  --border-focus-ring: 0.1rem solid #8f33ff66; /* 聚焦描边 */
  --border-mystic-divider: 0.1rem solid var(--neutral-divider); /* 分割线 */

  /* ================= 滚动条样式 ================= */
  --scrollbar-width: 0.2rem;
  --scrollbar-thumb: var(--interactive-primary);
  --scrollbar-thumb-hover: var(--interactive-primary-hover);
  --scrollbar-track: var(--background-surface);

  /* 新增魔法特效色 */
  --magic-gold: #ffd700;
  --magic-pink: #ff69b4;
  --magic-blue: #66d9ff;
  --magic-circle-size: 13.75rem;

  /* 单选按钮专属变量 */
  --radio-outer-border: #a45de2;
  --radio-inner-circle: var(--magic-gold);
  --radio-magic-circle: rgba(255, 215, 0, 0.7);
  --radio-magic-dot: var(--magic-pink);
  --radio-rune: var(--magic-pink);
  --radio-selected-border: var(--magic-gold);
  --radio-selected-text: var(--magic-gold);
  --radio-background: linear-gradient(145deg, #1a0a2e, #2d114f);
  --radio-selected-background: linear-gradient(145deg, #2a0f4d, #3c1a6d);
  --radio-hover-glow: 0 0 1.5rem rgba(143, 51, 255, 0.6);

  /* ============== 输入框专属变量体系 ============== */
  --input-rune-color: var(--magic-pink);
  --input-rune-glow: 0 0 0.625rem rgba(var(--magic-pink-rgb), 0.5);
  --input-field-bg: #190a30cc;
  --input-field-border: 0.125rem solid var(--radio-outer-border);
  --input-placeholder: rgba(var(--text-secondary-rgb), 0.7);
  --input-focus-border: 0.125rem solid var(--interactive-primary);
  --input-focus-shadow:
    0 0 1.25rem rgba(var(--interactive-primary-rgb), 0.5),
    inset 0 0 0.625rem rgba(var(--interactive-primary-rgb), 0.3);
  --input-warning-border: 0.125rem solid var(--magic-gold);
  --input-warning-shadow:
    0 0 0.9375rem rgba(var(--magic-gold-rgb), 0.3),
    inset 0 0 0.5rem rgba(var(--magic-gold-rgb), 0.2);
  --input-danger-border: 0.125rem solid var(--interactive-danger);
  --input-danger-shadow:
    0 0 0.9375rem rgba(var(--interactive-danger-rgb), 0.5),
    inset 0 0 0.5rem rgba(var(--interactive-danger-rgb), 0.3);
  --counter-bg: rgba(var(--interactive-primary-rgb), 0.2);
  --counter-text: var(--text-secondary);
  --counter-warning-bg: rgba(var(--magic-gold-rgb), 0.2);
  --counter-warning-text: var(--magic-gold);
  --counter-danger-bg: rgba(var(--interactive-danger-rgb), 0.2);
  --counter-danger-text: var(--interactive-danger);

  /* 标签变量 */
  --tag-bg: rgba(143, 51, 255, 0.25);
  --tag-border: 1px solid rgba(164, 93, 226, 0.5);
  --tag-text: #f2f2f2;
  --tag-remove-color: var(--interactive-danger);
  --tag-hover-bg: rgba(143, 51, 255, 0.4);
  --tag-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  /* ============== 日期选择器专属变量体系 ============== */
  --datepicker-rune-color: var(--magic-pink);
  --datepicker-rune-glow: 0 0 0.625rem rgba(var(--magic-pink-rgb), 0.7);
  --datepicker-input-bg: #190a30cc;
  --datepicker-input-border: 0.125rem solid var(--radio-outer-border);
  --datepicker-input-shadow:
    0 0.25rem 0.375rem rgba(0, 0, 0, 0.1),
    inset 0 0 0.3125rem rgba(var(--interactive-primary-rgb), 0.3);
  --datepicker-input-hover-border: 0.125rem solid var(--interactive-primary);
  --datepicker-input-hover-shadow:
    0 0 0.9375rem rgba(var(--interactive-primary-rgb), 0.7),
    inset 0 0 0.5rem rgba(var(--interactive-primary-rgb), 0.3);
  --datepicker-calendar-bg: var(--background-surface);
  --datepicker-calendar-border: 0.125rem solid var(--interactive-primary);
  --datepicker-calendar-shadow:
    0 0.625rem 1.5625rem rgba(0, 0, 0, 0.5),
    inset 0 0 1.25rem rgba(var(--interactive-primary-rgb), 0.3);
  --datepicker-header-accent: linear-gradient(
    90deg,
    var(--magic-pink),
    var(--interactive-primary),
    var(--magic-blue)
  );
  --datepicker-weekday-color: var(--text-secondary);
  --datepicker-weekend-color: var(--magic-blue);
  --datepicker-day-bg: rgba(var(--interactive-primary-rgb), 0.1);
  --datepicker-day-hover-bg: rgba(var(--interactive-primary-rgb), 0.3);
  --datepicker-today-color: var(--magic-gold);
  --datepicker-today-border: 0.0625rem solid var(--magic-gold);
  --datepicker-selected-bg: linear-gradient(
    135deg,
    var(--interactive-primary),
    var(--magic-blue)
  );
  --datepicker-selected-shadow:
    0 0 0.9375rem var(--interactive-primary),
    inset 0 0 0.625rem rgba(var(--interactive-primary-rgb), 0.3);
  --datepicker-preselected-bg: rgba(var(--magic-blue-rgb), 0.2);
  --datepicker-preselected-border: 0.0625rem solid var(--magic-blue);
  --datepicker-button-bg: rgba(var(--interactive-primary-rgb), 0.2);
  --datepicker-button-border: 0.0625rem solid var(--interactive-primary);
  --datepicker-clear-button-bg: rgba(var(--interactive-danger-rgb), 0.2);
  --datepicker-clear-button-border: 0.0625rem solid var(--interactive-danger);

  /* ============== 选择框专属变量体系 ============== */
  --select-rune-color: var(--magic-pink);
  --select-rune-glow: 0 0 0.625rem rgba(var(--magic-pink-rgb), 0.5);
  --select-field-bg: #190a30cc;
  --select-field-border: 0.125rem solid var(--radio-outer-border);
  --select-field-shadow:
    0 0 0.625rem rgba(75, 0, 130, 0.3),
    inset 0 0 0.3125rem rgba(var(--interactive-primary-rgb), 0.2);
  --select-open-border: 0.125rem solid var(--interactive-primary);
  --select-open-bg: rgba(35, 15, 65, 0.9);
  --select-option-bg: rgba(70, 20, 120, 0.2);
  --select-option-hover-bg: rgba(var(--interactive-primary-rgb), 0.4);
  --select-option-selected-bg: linear-gradient(
    90deg,
    rgba(var(--interactive-primary-rgb), 0.6),
    rgba(var(--interactive-primary-rgb), 0.4)
  );
  --select-dropdown-bg: rgba(18, 7, 35, 0.95);
  --select-dropdown-border: 0.125rem solid var(--interactive-primary);
  --select-dropdown-header: linear-gradient(
    90deg,
    var(--magic-pink),
    var(--interactive-primary),
    var(--magic-blue)
  );

  /* ============== 图片上传组件专属变量体系 ============== */
  --upload-primary: #8f33ff;
  --upload-primary-hover: #ab66ff;
  --upload-primary-active: #7300ff;
  --upload-bg: #1f1d2f;
  --upload-surface: #29273f;
  --upload-border: #d5dbe2;
  --upload-text: #f2f2f2;
  --upload-text-light: #bcbcc2;
  --upload-magic-glow: 0 0 8px rgba(143, 51, 255, 0.5);
  --upload-danger: #d20f39;

  /* ============== 裁剪组件专用变量 ============== */
  --cropper-primary: #8f33ff;
  --cropper-primary-rgb: 143, 51, 255;
  --cropper-primary-light: #b57aff;
  --cropper-secondary: #6a3093;
  --cropper-secondary-rgb: 106, 48, 147;
  --cropper-accent: #ffd700;
  --cropper-danger: #ff335f;
  --cropper-danger-rgb: 255, 51, 95;
  --cropper-success: #4caf50;
  --cropper-success-rgb: 76, 175, 80;
  --cropper-bg: linear-gradient(145deg, #1a1a2e, #16213e);
  --cropper-border: #a45de2;
  --cropper-text: #f2f2f2;
  --cropper-text-secondary: #bcbcc2;
  --cropper-card-bg: rgba(25, 25, 45, 0.8);
  --cropper-panel-bg: rgba(35, 15, 65, 0.7);
  --cropper-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  --cropper-overlay: rgba(10, 8, 24, 0.85);
  --cropper-radius: 16px;
  --cropper-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  /* ============== 横幅标题组件专用变量 ============== */
  --banner-height: 8rem;
  --banner-padding: var(--spacing-5, 1.25rem);
  --banner-element-spacing: var(--spacing-2, 0.5rem);
  --banner-border-width: var(--border-width-md, 0.125rem);
  --banner-border-radius: var(--border-radius-md, 1rem);
}
