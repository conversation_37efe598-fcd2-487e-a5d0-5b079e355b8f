<template>
  <div class="page-container">
    <Head>
      <Title>搜索-{{ key }}</Title>
    </Head>
    <MoblieNavBar :is-search="true" />
    <main>
      <!-- 搜索结果为空时显示提示 -->
      <div v-if="!key" class="search-home">
        <!-- 搜索提示 -->
        <div class="search-prompt">
          <div class="search-icon">
            <IconSvg name="search" size="4rem" color="var(--text-secondary)" />
          </div>
          <h3>搜索发现精彩内容</h3>
          <p>输入关键词搜索图片、帖子和用户</p>
        </div>

        <!-- 热门搜索词 -->
        <div class="hot-keywords" v-if="hotKeywords.length > 0">
          <h4>热门搜索</h4>
          <div class="keywords-list">
            <span
              v-for="keyword in hotKeywords"
              :key="keyword"
              class="keyword-tag"
              @click="handleKeywordClick(keyword)"
            >
              {{ keyword }}
            </span>
          </div>
        </div>
      </div>

      <!-- 有搜索关键词时显示标签页 -->
      <div v-else class="search-results">
        <RikkaTabs
          :tabs="tabs"
          :initial-tab="activeTabIndex"
          @active-tab="handleTabChange"
        >
          <!-- 图片搜索结果 -->
          <template #images>
            <RikkaImagesWaterfallGallery
              :items="imagesArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="imagesLoading"
              :has-more="hasMoreImages"
              @load-more="handleLoadMoreImages"
            >
              <template #item="{ items, columnWidth }">
                <div v-for="item in items" :key="item.id" class="custom-item">
                  <img
                    :src="item.url"
                    :alt="item.filename"
                    :style="{ width: columnWidth + 'px' }"
                    loading="lazy"
                    @click="
                      $router.push({
                        path: '/mobile/photos/detail',
                        query: { id: item.id },
                      })
                    "
                  />
                </div>
              </template>
            </RikkaImagesWaterfallGallery>
          </template>

          <!-- 帖子搜索结果 -->
          <template #posts>
            <RikkaPostsWaterfallGallery
              :posts="postsArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="postsLoading"
              :has-more="hasMorePosts"
              @load-more="handleLoadMorePosts"
            >
              <template #item="{ items, columnWidth }">
                <MobilePostCard
                  v-for="post in items"
                  :key="post.id"
                  :post="post"
                  :fixed-width="columnWidth"
                />
              </template>
            </RikkaPostsWaterfallGallery>
          </template>

          <!-- 用户搜索结果 -->
          <template #users>
            <RikkaUsersGrid
              :users="usersArray"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="usersLoading"
              :has-more="hasMoreUsers"
              @load-more="handleLoadMoreUsers"
            />
          </template>
        </RikkaTabs>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  keepalive: true,
});

const router = useRouter();
const route = useRoute();
const key = ref(route.query.key as string);

// 热门搜索关键词
const hotKeywords = ref<string[]>([]);

// 标签页配置
const tabs = ref([
  { title: '图片', slotName: 'images' },
  { title: '帖子', slotName: 'posts' },
  { title: '用户', slotName: 'users' },
]);

// 当前活跃的标签页索引
const activeTabIndex = ref(0);

// 图片搜索相关状态
const imagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const imagesLoading = ref(false);
const imagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 20,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});

// 帖子搜索相关状态
const postsArray = ref<PostsWaterfallGalleryItem[]>([]);
const postsLoading = ref(false);
const postsPaginated = ref<Paginated<PostsSortField>>({
  page: 0,
  pageSize: 20,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});

// 用户搜索相关状态
const usersArray = ref<UsersList[]>([]);
const usersLoading = ref(false);
const usersPaginated = ref<Paginated<UserSortField>>({
  page: 0,
  pageSize: 20,
  totalCount: 0,
  totalPage: 1,
  sortField: 'visitedCount',
  sortOrder: 'desc',
});

// 计算是否还有更多数据
const hasMoreImages = computed(
  () => imagesPaginated.value.page < imagesPaginated.value.totalPage
);
const hasMorePosts = computed(
  () => postsPaginated.value.page < postsPaginated.value.totalPage
);
const hasMoreUsers = computed(
  () => usersPaginated.value.page < usersPaginated.value.totalPage
);

// 获取热门搜索关键词
const getHotKeywords = async () => {
  try {
    const keywords = await useApi().getSearchHotwords();
    hotKeywords.value = keywords || [];
  } catch (err) {
    console.error('获取热门搜索词失败:', err);
  }
};

// 处理关键词点击
const handleKeywordClick = (keyword: string) => {
  router.push({
    path: '/mobile/search',
    query: { key: keyword },
  });
};

// 处理标签页切换
const handleTabChange = (index: number) => {
  activeTabIndex.value = index;
};

// 获取图片列表数据
const getImagesList = async (query?: PhotosListQuery) => {
  try {
    const { list, ...res } = await useApi().getPhotosList(query);

    // 如果是第一页，清空原有数据
    if (!query || !query.page || query.page === 1) {
      imagesArray.value = [];
    }

    imagesArray.value.push(...list);
    imagesPaginated.value = res;
  } catch (err) {
    console.error('获取图片列表失败:', err);
  }
};

// 获取帖子列表数据
const getPostsList = async (query?: PostsListQuery) => {
  try {
    const { list, ...res } = await useApi().getPostList(query);

    // 处理每条帖子的关联图片信息
    const newData = await Promise.all(
      list.map(async (v) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;

        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );

    // 如果是第一页，清空原有数据
    if (!query || !query.page || query.page === 1) {
      postsArray.value = [];
    }

    postsArray.value.push(...newData);
    postsPaginated.value = res;
  } catch (err) {
    console.error('获取帖子列表失败:', err);
  }
};

// 获取用户列表数据
const getUsersList = async (query?: UserListQuery) => {
  try {
    const { list, ...res } = await useApi().getUserList(query);

    // 如果是第一页，清空原有数据
    if (!query || !query.page || query.page === 1) {
      usersArray.value = [];
    }

    usersArray.value.push(...list);
    usersPaginated.value = res;
  } catch (err) {
    console.error('获取用户列表失败:', err);
  }
};

// 处理图片加载更多
const handleLoadMoreImages = async () => {
  if (imagesLoading.value || !hasMoreImages.value || !key.value) {
    return;
  }
  imagesLoading.value = true;

  await getImagesList({
    page: imagesPaginated.value.page + 1,
    pageSize: 20,
    keyword: key.value,
  });

  imagesLoading.value = false;
};

// 处理帖子加载更多
const handleLoadMorePosts = async () => {
  if (postsLoading.value || !hasMorePosts.value || !key.value) {
    return;
  }
  postsLoading.value = true;

  await getPostsList({
    page: postsPaginated.value.page + 1,
    pageSize: 20,
    keyword: key.value,
  });

  postsLoading.value = false;
};

// 处理用户加载更多
const handleLoadMoreUsers = async () => {
  if (usersLoading.value || !hasMoreUsers.value || !key.value) {
    return;
  }
  usersLoading.value = true;

  await getUsersList({
    page: usersPaginated.value.page + 1,
    pageSize: 20,
    keyword: key.value,
  });

  usersLoading.value = false;
};

// 重置所有搜索数据
const resetAllData = () => {
  imagesArray.value = [];
  postsArray.value = [];
  usersArray.value = [];

  imagesPaginated.value.page = 0;
  postsPaginated.value.page = 0;
  usersPaginated.value.page = 0;
};

// 根据当前标签页加载数据
const loadCurrentTabData = async () => {
  if (!key.value) return;

  switch (activeTabIndex.value) {
    case 0: // 图片
      if (imagesArray.value.length === 0) {
        await getImagesList({ page: 1, pageSize: 20, keyword: key.value });
      }
      break;
    case 1: // 帖子
      if (postsArray.value.length === 0) {
        await getPostsList({ page: 1, pageSize: 20, keyword: key.value });
      }
      break;
    case 2: // 用户
      if (usersArray.value.length === 0) {
        await getUsersList({ page: 1, pageSize: 20, keyword: key.value });
      }
      break;
  }
};

// 监听搜索关键词变化
watch(
  () => route.query.key,
  async (newKey) => {
    key.value = newKey as string;
    if (newKey) {
      // 重置所有数据
      resetAllData();
      // 根据当前标签页加载对应数据
      await loadCurrentTabData();
    }
  },
  { immediate: true }
);

// 监听标签页切换
watch(activeTabIndex, async () => {
  if (key.value) {
    await loadCurrentTabData();
  }
});

// 页面初始化
onMounted(() => {
  getHotKeywords();
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background-color: var(--background-base);
  color: var(--text-primary);

  > main {
    flex: 1;
    overflow: auto;
    background: var(--background-elevated);
  }
}

// 搜索主页样式
.search-home {
  padding: 2rem 1.5rem;
  height: 100%;
  overflow-y: auto;
}

// 搜索提示样式
.search-prompt {
  text-align: center;
  margin-bottom: 3rem;

  .search-icon {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
  }

  p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.5;
  }
}

// 热门搜索样式
.hot-keywords {
  margin-bottom: 2.5rem;

  h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
  }

  .keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;

    .keyword-tag {
      padding: 0.5rem 1rem;
      background: var(--background-surface);
      border: 1px solid var(--border-primary);
      border-radius: 1.5rem;
      font-size: 0.85rem;
      color: var(--text-primary);
      cursor: pointer;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.95);
        background: var(--interactive-primary);
        color: var(--text-on-primary);
      }
    }
  }
}

// 搜索结果样式
.search-results {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 自定义图片项样式
.custom-item {
  img {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    transition: transform 0.2s ease;
    cursor: pointer;
    margin: 0 auto;

    &:active {
      transform: scale(0.98);
    }
  }
}
</style>

