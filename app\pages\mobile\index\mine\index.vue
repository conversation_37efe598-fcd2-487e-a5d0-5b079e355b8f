<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-我的</Title>
    </Head>
    <!-- 已登录状态显示 -->
    <MobileDrawer :show-nav="false" ref="drawerRef" />
    <div class="is-login" v-if="authStore.getIsLogin()">
      <!-- 用户信息头部区域 -->
      <div class="user-header">
        <!-- 背景图片 -->
        <div
          class="background-image"
          :style="{
            backgroundImage: `url(${userInfo?.background + '?width=800'})`,
          }"
        >
          <!-- 用户信息内容 -->
          <div class="user-content">
            <!-- 用户头像 -->
            <div class="avatar-container">
              <img
                :src="userInfo?.avatar || '/default-avatar.png'"
                :alt="userInfo?.nickname"
                class="user-avatar"
              />
            </div>

            <!-- 用户信息 -->
            <div class="user-info">
              <!-- 用户名 -->
              <div class="username-row">
                <h2 class="username">{{ userInfo?.nickname }}</h2>
              </div>

              <!-- 用户ID和性别年龄 -->
              <div class="user-details">
                <div class="user-id-gender-row">
                  <span class="uid-text">UID: {{ userInfo?.uid }}</span>
                  <div class="gender-age" v-if="userInfo?.gender || userAge">
                    <IconSvg
                      :name="userGender.name"
                      size="1rem"
                      :color="userGender.color"
                    />
                    <span>{{ userAge }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 菜单按钮 -->
          <div class="menu-button" @click="drawerRef?.openDrawer()">
            <IconSvg name="menu" size="1.5rem" color="var(--text-secondary)" />
          </div>
        </div>
      </div>

      <!-- 统计数据区域 -->
      <div class="stats-section">
        <div class="stat-item">
          <div class="stat-number">{{ userInfo?.followingsCount || 0 }}</div>
          <div class="stat-label">关注</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ userInfo?.followersCount || 0 }}</div>
          <div class="stat-label">粉丝</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">
            {{ formatNumber(userInfo?.totalDownloads || 0) }}
          </div>
          <div class="stat-label">热度</div>
        </div>

        <div class="edit-profile">
          <RippleButton
            class="editor-btn"
            ripple-color="#fff9"
            @click="router.push('/mobile/mine/edit')"
            >编辑资料</RippleButton
          >
        </div>
      </div>

      <!-- 个人简介 -->
      <div class="bio-section" v-if="userInfo?.bio">
        <p>{{ userInfo.bio }}</p>
      </div>

      <!-- 标签页区域 -->
      <div class="tabs">
        <RikkaTabs
          :tabs="tabs"
          :initial-tab="activeTabIndex"
          @active-tab="handleTabChange"
        >
          <template #my-images>
            <RikkaImagesWaterfallGallery
              :items="myImagesArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="myImagesLoading"
              :has-more="hasMoreMyImages"
              @load-more="handleLoadMoreMyImages"
            />
          </template>
          <template #my-posts>
            <RikkaPostsWaterfallGallery
              :posts="myPostsArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="myPostsLoading"
              :has-more="hasMoreMyPosts"
              @load-more="handleLoadMoreMyPosts"
            >
              <template #item="{ items, columnWidth }">
                <MobilePostCard
                  v-for="post in items"
                  :key="post.id"
                  :post="post"
                  :fixed-width="columnWidth"
                />
              </template>
            </RikkaPostsWaterfallGallery>
          </template>
          <template #liked-images>
            <RikkaImagesWaterfallGallery
              :items="likedImagesArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="likedImagesLoading"
              :has-more="hasMoreLikedImages"
              @load-more="handleLoadMoreLikedImages"
            />
          </template>
          <template #collected-images>
            <RikkaImagesWaterfallGallery
              :items="collectedImagesArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="collectedImagesLoading"
              :has-more="hasMoreCollectedImages"
              @load-more="handleLoadMoreCollectedImages"
            />
          </template>
          <template #liked-posts>
            <RikkaPostsWaterfallGallery
              :posts="likedPostsArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="likedPostsLoading"
              :has-more="hasMoreLikedPosts"
              @load-more="handleLoadMoreLikedPosts"
            >
              <template #item="{ items, columnWidth }">
                <MobilePostCard
                  v-for="post in items"
                  :key="post.id"
                  :post="post"
                  :fixed-width="columnWidth"
                />
              </template>
            </RikkaPostsWaterfallGallery>
          </template>
          <template #collected-posts>
            <RikkaPostsWaterfallGallery
              :posts="collectedPostsArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="collectedPostsLoading"
              :has-more="hasMoreCollectedPosts"
              @load-more="handleLoadMoreCollectedPosts"
            >
              <template #item="{ items, columnWidth }">
                <MobilePostCard
                  v-for="post in items"
                  :key="post.id"
                  :post="post"
                  :fixed-width="columnWidth"
                />
              </template>
            </RikkaPostsWaterfallGallery>
          </template>
        </RikkaTabs>
      </div>
    </div>

    <!-- 未登录状态显示 -->
    <div v-else class="is-not-login"><AppUnauthPlaceholder /></div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'mobile',
  keepalive: true,
});

const drawerRef = ref<MobileDrawerExpose | null>(null);

// 使用认证状态存储
const authStore = useAuthStore();
const tabStore = useTabStore();
const router = useRouter();

// 计算用户性别图标和颜色
const userGender = computed<{
  color: string;
  name: 'male' | 'female' | 'hidden';
}>(() => {
  switch (authStore.getAuth()?.gender) {
    case 'male':
      return {
        color: '#4285F4',
        name: 'male',
      };
    case 'female':
      return {
        color: '#DB4437',
        name: 'female',
      };
    default:
      return {
        color: '#fff',
        name: 'hidden',
      };
  }
});

// 计算用户年龄
const userAge = computed(() => {
  return calculateAge(authStore.getAuth()?.birthday);
});

// 计算属性获取用户信息
const userInfo = computed(() => {
  return authStore.getAuth();
});

// 格式化数字显示
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num.toString();
};

// 标签页配置 - 动态计算标题中的计数
const tabs = computed(() => [
  {
    title: '我的图片 ' + (userInfo.value?.photosCount || 0),
    slotName: 'my-images',
  },
  {
    title: '我的帖子 ' + (userInfo.value?.contentsCount || 0),
    slotName: 'my-posts',
  },
  {
    title: '点赞图片 ' + (userInfo.value?.likePhotosCount || 0),
    slotName: 'liked-images',
  },
  {
    title: '收藏图片 ' + (userInfo.value?.favoritePhotosCount || 0),
    slotName: 'collected-images',
  },
  {
    title: '点赞帖子 ' + (userInfo.value?.likeContentsCount || 0),
    slotName: 'liked-posts',
  },
  {
    title: '收藏帖子 ' + (userInfo.value?.favoriteContentsCount || 0),
    slotName: 'collected-posts',
  },
]);

// 当前活跃的标签页索引
const activeTabIndex = ref(0);

// 处理标签页切换
const handleTabChange = (index: number) => {
  activeTabIndex.value = index;
  tabStore.setMineTab(index);
};

// 获取用户信息方法
const getUserInfo = () => {
  try {
    useApi().getMyInfo();
  } catch (err) {
    throw err;
  }
};

// 初始化获取用户信息
getUserInfo();

/** 用户自己的图片列表 */
// 响应式图片数组
const myImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
// 加载状态
const myImagesLoading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMoreMyImages = computed(
  () => myImagesPaginated.value.page < myImagesPaginated.value.totalPage
);
// 分页信息
const myImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});

const getMyPhotosList = async (query?: PhotosListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useApi().getUserPhotosList(
      authStore.authStore?.uid,
      query
    );
    myImagesArray.value.push(...list);
    myImagesPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

const handleLoadMoreMyImages = async () => {
  if (myImagesLoading.value || !hasMoreMyImages.value) {
    return;
  }
  myImagesLoading.value = true;
  await getMyPhotosList({
    page: myImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  myImagesLoading.value = false;
};

/** 用户自己的帖子列表 */
const myPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const myPostsLoading = ref(false);
const hasMoreMyPosts = computed(
  () => myPostsPaginated.value.page < myPostsPaginated.value.totalPage
);
const myPostsPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});

const getMyPostsList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useApi().getUserPostList(
      authStore.authStore?.uid,
      query
    );
    const newData = await Promise.all(
      list.map(async (v) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;
        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );
    myPostsArray.value.push(...newData);
    myPostsPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

const handleLoadMoreMyPosts = async () => {
  if (myPostsLoading.value || !hasMoreMyPosts.value) {
    return;
  }
  myPostsLoading.value = true;
  await getMyPostsList({
    page: myPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  myPostsLoading.value = false;
};

/** 点赞图片列表 */
const likedImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const likedImagesLoading = ref(false);
const likedImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const hasMoreLikedImages = computed(
  () => likedImagesPaginated.value.page < likedImagesPaginated.value.totalPage
);

const getLikedImagesList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getLikePhotoList(
      authStore.authStore?.uid,
      query
    );
    likedImagesArray.value.push(...list);
    likedImagesPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

const handleLoadMoreLikedImages = async () => {
  if (likedImagesLoading.value || !hasMoreLikedImages.value) return;
  likedImagesLoading.value = true;
  await getLikedImagesList({
    page: likedImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  likedImagesLoading.value = false;
};

/** 收藏图片列表 */
const collectedImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const collectedImagesLoading = ref(false);
const collectedImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const hasMoreCollectedImages = computed(
  () =>
    collectedImagesPaginated.value.page <
    collectedImagesPaginated.value.totalPage
);

const getCollectedImagesList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getFavoritePhotoList(
      authStore.authStore?.uid,
      query
    );
    collectedImagesArray.value.push(...list);
    collectedImagesPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

const handleLoadMoreCollectedImages = async () => {
  if (collectedImagesLoading.value || !hasMoreCollectedImages.value) return;
  collectedImagesLoading.value = true;
  await getCollectedImagesList({
    page: collectedImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  collectedImagesLoading.value = false;
};

/** 点赞帖子列表 */
const likedPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const likedPostsLoading = ref(false);
const likedPostsPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const hasMoreLikedPosts = computed(
  () => likedPostsPaginated.value.page < likedPostsPaginated.value.totalPage
);

const getLikedPostsList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getLikeContentList(
      authStore.authStore?.uid,
      query
    );
    const newData = await Promise.all(
      list.map(async (v: any) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;
        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );
    likedPostsArray.value.push(...newData);
    likedPostsPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

const handleLoadMoreLikedPosts = async () => {
  if (likedPostsLoading.value || !hasMoreLikedPosts.value) return;
  likedPostsLoading.value = true;
  await getLikedPostsList({
    page: likedPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  likedPostsLoading.value = false;
};

/** 收藏帖子列表 */
const collectedPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const collectedPostsLoading = ref(false);
const collectedPostsPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const hasMoreCollectedPosts = computed(
  () =>
    collectedPostsPaginated.value.page < collectedPostsPaginated.value.totalPage
);

const getCollectedPostsList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getFavoriteContentList(
      authStore.authStore?.uid,
      query
    );
    const newData = await Promise.all(
      list.map(async (v: any) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;
        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );
    collectedPostsArray.value.push(...newData);
    collectedPostsPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

const handleLoadMoreCollectedPosts = async () => {
  if (collectedPostsLoading.value || !hasMoreCollectedPosts.value) return;
  collectedPostsLoading.value = true;
  await getCollectedPostsList({
    page: collectedPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  collectedPostsLoading.value = false;
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  background: var(--background-primary);

  > .is-not-login {
    height: 100%;
  }

  .is-login {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

// 用户信息头部区域
.user-header {
  position: relative;
  height: 15rem;
  overflow: hidden;

  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column-reverse;

    // 添加渐变遮罩
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.6) 100%
      );
    }
  }

  .user-content {
    position: relative;
    z-index: 2;
    padding: 1.25rem;
    display: flex;
    align-items: flex-end;
    gap: 0.9375rem;
  }

  .menu-button {
    position: absolute;
    right: 1rem;
    top: 1rem;
    padding: 1rem;
    border-radius: 50%;
    background-color: #fff2;
    z-index: 99;
  }

  .avatar-container {
    flex-shrink: 0;

    .user-avatar {
      width: 8rem;
      height: 8rem;
      border-radius: 50%;
      border: 0.1875rem solid rgba(255, 255, 255, 0.8);
      object-fit: cover;
      object-position: center;
      transition: var(--transition);

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .user-info {
    flex: 1;
    display: flex;
    height: 100%;
    flex-direction: column;
    justify-content: center;
    color: var(--text-primary);

    .username-row {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;

      .username {
        font-size: 1.7rem;
        font-weight: bold;
        margin: 0;
        text-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.5);
      }
    }

    .user-details {
      display: flex;
      flex-direction: column;
      gap: 0.375rem;

      .user-id-gender-row {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .uid-text {
          font-size: 1rem;
          opacity: 0.9;
          text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.5);
        }

        .gender-age {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.25rem 0.5rem;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 0.75rem;
          font-size: 0.8rem;
          backdrop-filter: blur(0.25rem);
          width: fit-content;

          span {
            text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.5);
          }
        }
      }
    }
  }
}

// 统计数据区域
.stats-section {
  display: flex;
  align-items: center;
  padding: 0.9375rem 1.25rem;
  background: var(--background-elevated);
  border-bottom: 0.0625rem solid var(--border-color);

  .stat-item {
    flex: 1;
    text-align: center;

    .stat-number {
      font-size: 1.2rem;
      font-weight: bold;
      color: var(--text-primary);
      margin-bottom: 0.125rem;
    }

    .stat-label {
      font-size: 0.8rem;
      color: var(--text-secondary);
    }
  }

  .edit-profile {
    flex: 3;
    display: flex;
    flex-direction: row-reverse;

    > .editor-btn {
      background-color: var(--neutral-divider);
      color: var(--text-primary);
      transition: all 0.3s ease-in-out;

      &:hover {
        background-color: var(--neutral-disabled);
      }
    }
  }
}

// 个人简介区域
.bio-section {
  padding: 0.5rem 1.25rem 1rem;
  background: var(--background-elevated);
  border-bottom: 0.0625rem solid var(--border-color);

  p {
    margin: 0;
    color: var(--text-primary);
    line-height: 1.5;
    font-size: 0.95rem;
  }
}

// 功能按钮区域
.action-buttons {
  display: flex;
  padding: 0.9375rem 1.25rem;
  background: var(--background-elevated);
  border-bottom: 0.0625rem solid var(--border-color);
  gap: 1.25rem;
  overflow-x: auto;

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    min-width: 3.75rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-0.125rem);
    }

    span {
      font-size: 0.8rem;
      color: var(--text-primary);
      text-align: center;
      white-space: nowrap;
    }
  }
}

// 标签页区域
.tabs {
  background: var(--background-elevated);
  border-bottom: 0.0625rem solid var(--border-color);
  flex: 1;
}
</style>

