<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-我的</Title>
    </Head>
    <!-- 已登录状态显示 -->
    <div class="is-login" v-if="authStore.getIsLogin()">
      <!-- 头部区域 -->
      <div class="header">
        <!-- 用户信息区域 -->
        <div class="user-info">
          <!-- 用户头像 -->
          <img :src="userInfo?.avatar" alt="" />
          <!-- 用户详细信息 -->
          <div class="info">
            <!-- 昵称 -->
            <div class="nickname">{{ userInfo?.nickname }}</div>
            <!-- 关注/粉丝/热度数据 -->
            <div class="follow-fans">
              <div class="follow">
                <span>关注</span> {{ userInfo?.followingsCount }}
              </div>
              <div class="fans">
                <span>粉丝</span> {{ userInfo?.followersCount }}
              </div>
              <div class="fans">
                <span>热度</span> {{ userInfo?.totalDownloads }}
              </div>
            </div>
            <!-- UID和性别年龄 -->
            <div class="uid-gender">
              <div class="uid">
                UID: <span>{{ userInfo?.uid }}</span>
              </div>
              <div class="gender-age">
                <IconSvg
                  :name="userGender.name"
                  size="1.2rem"
                  :color="userGender.color"
                />
                <span>{{ userAge }}岁</span>
              </div>
            </div>
            <!-- 个人简介 -->
            <div class="bio">{{ userInfo?.bio }}</div>
          </div>
        </div>
        <!-- 背景区域 -->
        <div
          class="background"
          :style="{
            backgroundImage: `linear-gradient(to left, #fff0, var(--background-elevated)), url(${userInfo?.background + '?width=800'})`,
          }"
        >
          <!-- 编辑资料按钮 -->
          <RippleButton
            class="editor-btn"
            @click="showUserInfoEditorModal = true"
            >编辑资料</RippleButton
          >
          <!-- 用户信息编辑弹窗 -->
          <AppUserInfoEditorModal
            :show="showUserInfoEditorModal"
            @close="showUserInfoEditorModal = false"
          />
        </div>
      </div>
      <!-- 标签页区域 -->
      <div class="tabs">
        <RikkaTabs
          :tabs="tabs"
          :initial-tab="tabStore.getMineTab()"
          @active-tab="
            (index) => {
              tabStore.setMineTab(index);
            }
          "
        >
          <template #my-images>
            <RikkaImagesWaterfallGallery
              :items="myImagesArray"
              :columns="0"
              :card-width="250"
              :gap="20"
              :loading="myImagesLoading"
              :has-more="hasMoreMyImages"
              @load-more="handleLoadMoreMyImages"
            >
            </RikkaImagesWaterfallGallery>
          </template>
          <template #my-posts>
            <RikkaPostsWaterfallGallery
              :posts="myPostsArray"
              :columns="0"
              :card-width="250"
              :gap="20"
              :loading="myPostsLoading"
              :has-more="hasMoreMyPosts"
              @load-more="handleLoadMoreMyPosts"
            />
          </template>
          <template #liked-images>
            <RikkaImagesWaterfallGallery
              :items="likedImagesArray"
              :columns="0"
              :card-width="250"
              :gap="20"
              :loading="likedImagesLoading"
              :has-more="hasMoreLikedImages"
              @load-more="handleLoadMoreLikedImages"
            />
          </template>
          <template #collected-images>
            <RikkaImagesWaterfallGallery
              :items="collectedImagesArray"
              :columns="0"
              :card-width="250"
              :gap="20"
              :loading="collectedImagesLoading"
              :has-more="hasMoreCollectedImages"
              @load-more="handleLoadMoreCollectedImages"
            />
          </template>
          <template #liked-posts>
            <RikkaPostsWaterfallGallery
              :posts="likedPostsArray"
              :columns="0"
              :card-width="250"
              :gap="20"
              :loading="likedPostsLoading"
              :has-more="hasMoreLikedPosts"
              @load-more="handleLoadMoreLikedPosts"
            />
          </template>
          <template #collected-posts>
            <RikkaPostsWaterfallGallery
              :posts="collectedPostsArray"
              :columns="0"
              :card-width="250"
              :gap="20"
              :loading="collectedPostsLoading"
              :has-more="hasMoreCollectedPosts"
              @load-more="handleLoadMoreCollectedPosts"
            />
          </template>
        </RikkaTabs>
      </div>
    </div>

    <!-- 未登录状态显示 -->
    <div v-else class="is-not-login"><AppUnauthPlaceholder /></div>
  </div>
</template>

<script lang="ts" setup>
// 设置页面元信息，使用首页布局
definePageMeta({
  layout: 'home',
  keepalive: true,
});

// 使用认证状态存储
const authStore = useAuthStore();
const tabStore = useTabStore();

// 计算用户性别图标和颜色
const userGender = computed<{
  color: string;
  name: 'male' | 'female' | 'hidden';
}>(() => {
  switch (authStore.getAuth()?.gender) {
    case 'male':
      return {
        color: '#4285F4',
        name: 'male',
      };
    case 'female':
      return {
        color: '#DB4437',
        name: 'female',
      };
    default:
      return {
        color: '#fff',
        name: 'hidden',
      };
  }
});

// 计算用户年龄
const userAge = computed(() => {
  return calculateAge(authStore.getAuth()?.birthday);
});

// 计算属性获取用户信息
const userInfo = computed(() => {
  return authStore.getAuth();
});

// 获取用户信息方法
const getUserInfo = () => {
  try {
    useApi().getMyInfo();
  } catch (err) {
    throw err;
  }
};

// 初始化获取用户信息
getUserInfo();

// 控制用户信息编辑弹窗显示状态
const showUserInfoEditorModal = ref(false);

// 标签页配置 - 动态计算标题中的计数
const tabs = computed(() => [
  {
    title: '我的图片 ' + (userInfo.value?.photosCount || 0),
    slotName: 'my-images',
  },
  {
    title: '我的帖子 ' + (userInfo.value?.contentsCount || 0),
    slotName: 'my-posts',
  },
  {
    title: '点赞图片 ' + (userInfo.value?.likePhotosCount || 0),
    slotName: 'liked-images',
  },
  {
    title: '收藏图片 ' + (userInfo.value?.favoritePhotosCount || 0),
    slotName: 'collected-images',
  },
  {
    title: '点赞帖子 ' + (userInfo.value?.likeContentsCount || 0),
    slotName: 'liked-posts',
  },
  {
    title: '收藏帖子 ' + (userInfo.value?.favoriteContentsCount || 0),
    slotName: 'collected-posts',
  },
]);

/** 用户自己的图片列表 */
// 响应式图片数组
const myImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
// 加载状态
const myImagesLoading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMoreMyImages = computed(
  () => myImagesPaginated.value.page < myImagesPaginated.value.totalPage
);
// 分页信息
const myImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const getMyPhotosList = async (query?: PhotosListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useApi().getUserPhotosList(
      authStore.authStore?.uid,
      query
    );
    myImagesArray.value.push(...list);
    myImagesPaginated.value = res;
  } catch (err) {
    throw err;
  }
};
const handleLoadMoreMyImages = async () => {
  if (myImagesLoading.value || !hasMoreMyImages.value) {
    return;
  }
  myImagesLoading.value = true;
  await getMyPhotosList({
    page: myImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  myImagesLoading.value = false;
};

/** 用户自己的帖子列表 */
// 响应式图片数组
const myPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
// 加载状态
const myPostsLoading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMoreMyPosts = computed(
  () => myPostsPaginated.value.page < myPostsPaginated.value.totalPage
);
// 分页信息
const myPostsPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const getMyPostsList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useApi().getUserPostList(
      authStore.authStore?.uid,
      query
    );
    // 处理每条帖子的关联图片信息
    const newData = await Promise.all(
      list.map(async (v) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;

        return {
          ...rest,
          photos, // 保留原有 photos 字段
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );

    myPostsArray.value.push(...newData);
    myPostsPaginated.value = res;
  } catch (err) {
    throw err;
  }
};
const handleLoadMoreMyPosts = async () => {
  if (myPostsLoading.value || !hasMoreMyPosts.value) {
    return;
  }
  myPostsLoading.value = true;
  await getMyPostsList({
    page: myPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  myPostsLoading.value = false;
};

/** 点赞图片列表 */
const likedImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const likedImagesLoading = ref(false);
const likedImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const hasMoreLikedImages = computed(
  () => likedImagesPaginated.value.page < likedImagesPaginated.value.totalPage
);
const getLikedImagesList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getLikePhotoList(
      authStore.authStore?.uid,
      query
    );
    likedImagesArray.value.push(...list);
    likedImagesPaginated.value = res;
  } catch (err) {
    throw err;
  }
};
const handleLoadMoreLikedImages = async () => {
  if (likedImagesLoading.value || !hasMoreLikedImages.value) return;
  likedImagesLoading.value = true;
  await getLikedImagesList({
    page: likedImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  likedImagesLoading.value = false;
};

/** 收藏图片列表 */
const collectedImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const collectedImagesLoading = ref(false);
const collectedImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const hasMoreCollectedImages = computed(
  () =>
    collectedImagesPaginated.value.page <
    collectedImagesPaginated.value.totalPage
);
const getCollectedImagesList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getFavoritePhotoList(
      authStore.authStore?.uid,
      query
    );
    collectedImagesArray.value.push(...list);
    collectedImagesPaginated.value = res;
  } catch (err) {
    throw err;
  }
};
const handleLoadMoreCollectedImages = async () => {
  if (collectedImagesLoading.value || !hasMoreCollectedImages.value) return;
  collectedImagesLoading.value = true;
  await getCollectedImagesList({
    page: collectedImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  collectedImagesLoading.value = false;
};

/** 点赞帖子列表 */
const likedPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const likedPostsLoading = ref(false);
const likedPostsPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const hasMoreLikedPosts = computed(
  () => likedPostsPaginated.value.page < likedPostsPaginated.value.totalPage
);
const getLikedPostsList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getLikeContentList(
      authStore.authStore?.uid,
      query
    );
    const newData = await Promise.all(
      list.map(async (v: any) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;
        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );
    likedPostsArray.value.push(...newData);
    likedPostsPaginated.value = res;
  } catch (err) {
    throw err;
  }
};
const handleLoadMoreLikedPosts = async () => {
  if (likedPostsLoading.value || !hasMoreLikedPosts.value) return;
  likedPostsLoading.value = true;
  await getLikedPostsList({
    page: likedPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  likedPostsLoading.value = false;
};

/** 收藏帖子列表 */
const collectedPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const collectedPostsLoading = ref(false);
const collectedPostsPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'desc',
});
const hasMoreCollectedPosts = computed(
  () =>
    collectedPostsPaginated.value.page < collectedPostsPaginated.value.totalPage
);
const getCollectedPostsList = async (query?: PostsListQuery) => {
  if (!authStore.authStore?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getFavoriteContentList(
      authStore.authStore?.uid,
      query
    );
    const newData = await Promise.all(
      list.map(async (v: any) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;
        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );
    collectedPostsArray.value.push(...newData);
    collectedPostsPaginated.value = res;
  } catch (err) {
    throw err;
  }
};
const handleLoadMoreCollectedPosts = async () => {
  if (collectedPostsLoading.value || !hasMoreCollectedPosts.value) return;
  collectedPostsLoading.value = true;
  await getCollectedPostsList({
    page: collectedPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  collectedPostsLoading.value = false;
};
</script>

<style lang="scss" scoped>
/* 页面容器样式 */
.page-container {
  height: 100%;

  /* 已登录状态样式 */
  > .is-login {
    height: 100%;
    display: flex;
    flex-direction: column;
    width: 100%;

    /* 头部区域样式 */
    > .header {
      width: 100%;
      display: flex;

      > div {
        height: 12rem;

        /* 用户信息区域样式 */
        &.user-info {
          padding: 1rem 2rem;
          display: flex;
          justify-content: center;
          align-items: center;
          min-width: 50%;

          > img {
            width: 6rem;
            height: 6rem;
            object-fit: cover;
            object-position: center;
            transition: var(--transition);
            border-radius: 50%;
          }

          > .info {
            flex: 1;
            padding: 1rem;

            > .nickname {
              font-size: 1.5rem;
            }

            > .follow-fans {
              display: flex;
              margin: 0.3rem 0 0.7rem;

              > div {
                margin-right: 2rem;
                cursor: pointer;

                > span {
                  color: var(--text-secondary);
                  margin-right: 0.2rem;
                }

                &:hover {
                  > span {
                    color: var(--text-primary);
                  }
                }
              }
            }

            > .uid-gender {
              font-size: 0.8rem;
              display: flex;
              align-items: center;
              margin-bottom: 0.3rem;

              > .uid {
                color: var(--text-secondary);
                margin-right: 2rem;

                > span {
                  margin-left: 0.1rem;
                }
              }

              > .gender-age {
                padding: 0.2rem 0.4rem;
                border-radius: 0.4rem;
                background-color: var(--background-floating);
                display: flex;
                align-items: center;

                > span {
                  margin-left: 0.2rem;
                }
              }
            }

            > .bio {
              font-size: 0.8rem;
            }
          }
        }

        /* 背景区域样式 */
        &.background {
          flex: 1;
          background-repeat: no-repeat;
          background-size: cover;
          background-position: center;
          display: flex;
          justify-content: end;
          align-items: end;
          padding: 2rem;

          > .editor-btn {
            background-color: var(--neutral-divider);
            color: var(--text-primary);
            transition: all 0.3s ease-in-out;

            &:hover {
              background-color: var(--neutral-disabled);
            }
          }
        }
      }
    }

    /* 标签页区域样式 */
    > .tabs {
      flex: 1;
      :deep(.tabs-container) {
        border-radius: 0 0 1rem 1rem;
      }
    }
  }

  /* 未登录状态样式 */
  .is-not-login {
    height: 100%;
  }
}
</style>

