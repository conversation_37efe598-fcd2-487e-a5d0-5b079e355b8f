<template>
  <div class="default-layout">
    <AppHearder>
      <template #left>
        <!-- 返回按钮 -->
        <div class="back">
          <div @click="$router.push(routerStore.goBack() || '/')">
            <IconSvg
              name="leftArrows"
              size="1.5rem"
              color="var(--text-secondary)"
            />
          </div>
        </div>
      </template>
    </AppHearder>
    <main class="main">
      <slot />
    </main>
    <AppFooter />
  </div>
</template>

<script lang="ts" setup>
const routerStore = useRouterStore();
</script>

<style lang="scss" scoped>
/* 整个页面的容器样式 */
.default-layout {
  width: 100%;
  height: 100%;

  display: flex;
  flex-direction: column;
  background-color: var(--background-base);
  color: var(--text-primary);

  .back {
    flex: 1;
    display: flex;

    > div {
      background-color: var(--background-elevated);
      padding: 0.7rem;
      border-radius: 1rem;
      cursor: pointer;
    }
  }

  /* 主要内容区域样式 */
  > .main {
    flex: 1;
    display: flex;
    margin: 0 0.5rem;
    min-height: 0; /* 修复flex容器滚动问题 */
    flex: 1;
    background: var(--background-gradient-1);
    border-radius: 1rem;
    display: flex; /* 新增flex容器 */
    flex-direction: column; /* 垂直排列 */
    overflow: hidden;

    /* 滚动区域 */
    > * {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch; /* 平滑滚动 */
    }
  }
}
</style>
