<template>
  <ClientOnly>
    <CardContainer class="image-container" ref="cardRef">
      <!-- 添加ref用于尺寸监听 -->
      <!-- 图片卡片主体 -->
      <CardBody
        class="image-body"
        :style="bodyStyle"
        @click="
          $router.push({ path: '/photos/detail', query: { id: item.id } })
        "
      >
        <CardItem :translate-z="60">
          <div class="image-info" :style="infoStyle">
            <div class="tags-wrapper">
              <div class="tags">
                <span v-for="(item, index) in item.tags" :key="index">{{
                  item
                }}</span>
              </div>
            </div>
            <div class="info">
              <div>
                <span>
                  <IconSvg
                    name="download"
                    :size="12"
                    :color="iconColor.default"
                  />
                </span>
                <span>{{ item.downloadCount }}</span>
              </div>
              <div>
                <span>
                  <IconSvg
                    name="favorite"
                    :size="12"
                    :color="
                      item.isFavorited ? iconColor.favorite : iconColor.default
                    "
                  />
                </span>
                <span>{{ item.favoriteCount }}</span>
              </div>
              <div>
                <span>
                  <IconSvg
                    name="like"
                    :size="12"
                    :color="item.isLiked ? iconColor.like : iconColor.default"
                  />
                </span>
                <span>{{ item.likeCount }}</span>
              </div>
              <div>
                <span>
                  <IconSvg
                    name="resolution"
                    :size="12"
                    :color="iconColor.default"
                  />
                </span>
                <span>{{
                  item.attributes.height + 'x' + item.attributes.width
                }}</span>
              </div>
            </div>
          </div>
        </CardItem>
      </CardBody>
    </CardContainer>
  </ClientOnly>
</template>

<script setup lang="ts">
// 定义组件props
interface Props {
  item: PhotosWaterfallGalleryItem;
  fixedWidth: number;
}

// 定义图标颜色映射
const iconColor = {
  default: '#CCCCCC',
  favorite: '#FFD700',
  like: '#FF5252',
};

// 获取父组件传递的props
const props = defineProps<Props>();

// 创建一个响应式ref用于引用卡片DOM元素
const cardRef = ref<HTMLElement | null>(null);

// 创建一个响应式ref用于存储实际高度
const actualHeight = ref<number>(0);

// 计算卡片高度以适应固定宽度
const computedHeight = computed(() => {
  return (
    props.fixedWidth *
    (props.item.attributes.height / props.item.attributes.width)
  );
});

// 计算背景图样式
const bodyStyle = computed(() => ({
  background: `url(${props.item.url}) no-repeat center center / cover`,
  width: `${props.fixedWidth}px`,
  height: `${computedHeight.value}px`,
}));

// 计算信息区域样式
const infoStyle = computed(() => ({
  width: `${props.fixedWidth * 0.8}px`,
  height: `${computedHeight.value * 0.8}px`,
}));

// 向父组件暴露获取实际高度的方法
defineExpose({
  getHeight: () => actualHeight.value,
});

// 创建ResizeObserver实例以监听尺寸变化
let resizeObserver: ResizeObserver | null = null;

// 在组件挂载后开始监听卡片元素的尺寸变化
onMounted(() => {
  resizeObserver = new ResizeObserver((entries) => {
    if (entries[0]) {
      actualHeight.value = entries[0].contentRect.height;
    }
  });

  if (cardRef.value) {
    resizeObserver.observe(cardRef.value);
  }
});

// 在组件卸载前停止监听尺寸变化以避免内存泄漏
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});
</script>

<style lang="scss" scoped>
// 定义卡片容器样式
.image-container {
  // 定义卡片主体悬停样式
  .image-body:hover {
    .image-info {
      opacity: 1;
      background: rgba(53, 0, 71, 0.526); // 深紫罗兰背景
      box-shadow: 0 0 20px rgba(138, 43, 226, 0.7); // 紫罗兰发光效果
      border: 1px solid rgba(138, 43, 226, 0.5); // 紫罗兰边框
    }
  }

  // 定义卡片主体样式
  .image-body {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 1rem;
    box-shadow: var(--shadow-neon-primary);
    cursor: pointer;

    // 定义卡片信息区域样式
    .image-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      opacity: 0;
      transition:
        opacity 0.3s ease,
        background-color 0.3s ease,
        box-shadow 0.3s ease,
        border 0.3s ease;
      border-radius: 1rem;
      padding: 0.5rem; // 增加内边距
      color: #e0b0ff; // 薰衣草色文字
      background: rgba(53, 0, 71, 0.7); // 深紫罗兰背景
      backdrop-filter: blur(4px); // 毛玻璃效果
      pointer-events: none; /* 禁止信息层响应鼠标事件 */
      // 使用box-sizing确保边框不影响布局
      box-sizing: border-box;
      // 预设透明边框避免hover时布局变化
      border: 1px solid transparent;

      // 标签包装容器样式
      > .tags-wrapper {
        flex: 1;
        position: relative;
        width: 100%;
        overflow: hidden;

        // 渐变遮罩效果
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 1.5rem;
          background: linear-gradient(
            to bottom,
            rgba(53, 0, 71, 0) 0%,
            rgba(53, 0, 71, 0.8) 80%,
            rgba(53, 0, 71, 1) 100%
          );
          pointer-events: none;
        }

        // 查看更多提示
        &::before {
          content: '⋮ 查看更多';
          position: absolute;
          bottom: 0.2rem;
          right: 0.5rem;
          color: #d8bfd8;
          font-size: 0.7rem;
          z-index: 1;
          opacity: 0.8;
        }
      }

      // 标签容器样式
      .tags {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        flex-wrap: wrap;
        width: 100%; // 宽度自适应
        gap: 0.4rem; // 使用gap替代margin
        padding-top: 0.5rem;

        // 单个标签样式
        > span {
          padding: 0.4rem 0.8rem;
          background: linear-gradient(
            145deg,
            rgba(74, 0, 128, 0.47),
            rgba(41, 0, 90, 0.46)
          ); // 紫罗兰渐变
          border-radius: 12px; // 更圆润的边框
          border: 1px solid #8a2be2; // 紫罗兰边框
          color: #d8bfd8; // 蓟色文字
          font-weight: 600;
          box-shadow: 0 0 8px rgba(138, 43, 226, 0.4); // 紫罗兰发光效果
          transition: all 0.2s ease;
          flex-shrink: 0; // 防止标签缩小
        }
      }

      // 信息区域样式
      > .info {
        display: flex;
        text-align: center;
        width: 100%;
        background: rgba(65, 0, 100, 0.4); // 紫罗兰深色背景
        border-radius: 0 0 12px 12px; // 圆角边框
        border: 1px solid rgba(138, 43, 226, 0.3); // 紫罗兰边框
        justify-content: space-evenly;

        // 单个信息项样式
        > div {
          display: flex;
          align-items: center;

          :first-child {
            margin-right: 2px;
          }

          :last-child {
            font-size: 12px;
          }
        }
      }

      // 媒体查询以适应不同屏幕尺寸
      @media (max-height: 250px) {
        padding: 0.8rem; /* 减小内边距 */

        .tags-wrapper {
          &::before {
            font-size: 0.6rem;
            bottom: 0.1rem;
          }
        }

        .tags {
          > span {
            padding: 0.2rem 0.5rem; /* 减小标签尺寸 */
            font-size: 0.8rem; /* 减小标签文字 */
          }
        }

        .info {
          font-size: 0.8rem; /* 减小信息文字 */
        }
      }
    }
  }
}
</style>
