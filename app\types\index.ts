import type { Socket } from 'socket.io-client';
import type { MyFetchOptions, MyFetchRequest } from '~/plugins/request';

declare module '#app' {
  interface NuxtApp {
    /**
     * 封装的网络请求工具
     * @template T - 响应数据类型
     * @param {MyFetchRequest} url - 请求URL或配置对象
     * @param {MyFetchOptions} [options] - 可选请求配置
     * @returns {Promise<T>} 返回Promise，解析为响应数据
     */
    $request: <T = any>(
      url: MyFetchRequest,
      options?: MyFetchOptions
    ) => Promise<T>;

    /**
     * 全局消息提示工具
     * @param {setMessage} message - 消息配置对象
     */
    $message: (message: setMessage) => void;

    /**
     * 设备类型检测工具
     * @property {boolean} isMobile - 是否为移动设备
     * @property {boolean} isTablet - 是否为平板设备
     * @property {boolean} isDesktop - 是否为桌面设备
     * @property {DeviceType} type - 设备类型枚举
     */
    $device: {
      isMobile: boolean;
      isTablet: boolean;
      isDesktop: boolean;
      type: DeviceType;
    };

    /** 加载状态管理实例 */
    $loading: loadingState;

    /** socket.io实例 */
    $socket: {
      socket: Socket;
      state: {
        isConnected: boolean;
        transport: string;
        error: Error | null;
      };
    };

    /** 日志工具封装 */
    $logger: {
      log: (text: any, location?: string, force?: boolean) => void;
      info: (text: any, location?: string, force?: boolean) => void;
      warn: (text: any, location?: string, force?: boolean) => void;
      error: (text: any, location?: string, force?: boolean) => void;
      debug: (text: any, location?: string, force?: boolean) => void;
      dir: (obj: any, location?: string, force?: boolean) => void;
    };
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    /**
     * 封装的网络请求工具
     * @template T - 响应数据类型
     * @param {MyFetchRequest} url - 请求URL或配置对象
     * @param {MyFetchOptions} [options] - 可选请求配置
     * @returns {Promise<T>} 返回Promise，解析为响应数据
     */
    $request: <T = any>(
      url: MyFetchRequest,
      options?: MyFetchOptions
    ) => Promise<T>;

    /**
     * 全局消息提示工具
     * @param {setMessage} message - 消息配置对象
     */
    $message: (message: setMessage) => void;

    /**
     * 设备类型检测工具
     * @property {boolean} isMobile - 是否为移动设备
     * @property {boolean} isTablet - 是否为平板设备
     * @property {boolean} isDesktop - 是否为桌面设备
     * @property {DeviceType} type - 设备类型枚举
     */
    $device: {
      isMobile: boolean;
      isTablet: boolean;
      isDesktop: boolean;
      type: DeviceType;
    };

    /** 加载状态管理实例 */
    $loading: loadingState;

    /** socket.io实例 */
    $socket: {
      socket: Socket;
      state: {
        isConnected: boolean;
        transport: string;
        error: Error | null;
      };
    };

    /** 日志工具封装 */
    $logger: {
      log: (text: any, location?: string, force?: boolean) => void;
      info: (text: any, location?: string, force?: boolean) => void;
      warn: (text: any, location?: string, force?: boolean) => void;
      error: (text: any, location?: string, force?: boolean) => void;
      debug: (text: any, location?: string, force?: boolean) => void;
      dir: (obj: any, location?: string, force?: boolean) => void;
    };
  }
}
