export default defineNuxtPlugin((nuxtApp) => {
  /**
   * 在开发环境下打印日志的工具对象
   *
   * 提供多种日志级别输出方式，并支持可选的日志位置标识
   * 利用 Vite 的 import.meta.dev 属性判断当前环境，仅在开发环境下输出日志
   */
  const logger = {
    /**
     * 输出普通信息日志
     *
     * @param text 要打印的日志信息文本
     * @param location 日志打印位置，默认为 'Log'
     * @param force 是否强制打印（即使在生产环境）
     */
    log: (text: any, location?: string, force = false) => {
      if (import.meta.dev || force) {
        console.log((location || 'Log') + ':', text);
      }
    },

    /**
     * 输出信息日志
     *
     * @param text 要打印的日志信息文本
     * @param location 日志打印位置，默认为 'Info'
     * @param force 是否强制打印（即使在生产环境）
     */
    info: (text: any, location?: string, force = false) => {
      if (import.meta.dev || force) {
        console.info((location || 'Info') + ':', text);
      }
    },

    /**
     * 输出警告日志
     *
     * @param text 要打印的日志信息文本
     * @param location 日志打印位置，默认为 'Warn'
     * @param force 是否强制打印（即使在生产环境）
     */
    warn: (text: any, location?: string, force = false) => {
      if (import.meta.dev || force) {
        console.warn((location || 'Warn') + ':', text);
      }
    },

    /**
     * 输出错误日志
     *
     * @param text 要打印的日志信息文本
     * @param location 日志打印位置，默认为 'Error'
     * @param force 是否强制打印（即使在生产环境）
     */
    error: (text: any, location?: string, force = false) => {
      if (import.meta.dev || force) {
        console.error((location || 'Error') + ':', text);
      }
    },

    /**
     * 输出调试日志
     *
     * @param text 要打印的日志信息文本
     * @param location 日志打印位置，默认为 'Debug'
     * @param force 是否强制打印（即使在生产环境）
     */
    debug: (text: any, location?: string, force = false) => {
      if (import.meta.dev || force) {
        console.debug((location || 'Debug') + ':', text);
      }
    },

    /**
     * 格式化输出对象数据
     *
     * @param obj 要打印的对象数据
     * @param location 日志打印位置，默认为 'Dir'
     * @param force 是否强制打印（即使在生产环境）
     */
    dir: (obj: any, location?: string, force = false) => {
      if (import.meta.dev || force) {
        console.dir((location || 'Dir') + ':', obj);
      }
    },
  };

  nuxtApp.provide('logger', logger);
});

