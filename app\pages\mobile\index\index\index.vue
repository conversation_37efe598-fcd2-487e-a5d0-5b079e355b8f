<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-图片</Title>
    </Head>
    <MobileDrawer />
    <RikkaPullToRefresh @refresh="handleRefresh">
      <RikkaImagesWaterfallGallery
        :items="imagesArray"
        :columns="0"
        :card-width="Number($device.isMobile ? 140 : 300)"
        :gap="10"
        :loading="loading"
        :has-more="hasMore"
        @load-more="handleLoadMore"
      >
        <!-- 使用具名插槽自定义项目渲染 -->
        <template #item="{ items, columnWidth }">
          <!-- 这里可以完全自定义每个项目的渲染 -->
          <div v-for="item in items" :key="item.id" class="custom-item">
            <img
              :src="item.url"
              :alt="item.filename"
              :style="{ width: columnWidth + 'px' }"
              loading="lazy"
              @click="
                $router.push({
                  path: '/mobile/photos/detail',
                  query: { id: item.id },
                })
              "
            />
          </div>
        </template>
      </RikkaImagesWaterfallGallery>
    </RikkaPullToRefresh>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'mobile',
  keepalive: true,
});

const { $device } = useNuxtApp();

const imagesArray = ref<PhotosWaterfallGalleryItem[]>([]); // 图片数组
const loading = ref(false); // 加载状态
const hasMore = computed(
  () => paginated.value.page < paginated.value.totalPage
); // 是否有更多
const paginated = ref<Paginated>({
  page: 0,
  pageSize: 0,
  totalCount: 0,
  totalPage: 1,
  sortField: '',
  sortOrder: 'desc',
});

/** 获取图片列表 */
const getPhotosList = async (query?: PhotosListQuery) => {
  try {
    const { list, ...res } = await useApi().getPhotosList({
      ...query,
    });
    imagesArray.value.push(...list);
    paginated.value = res;
  } catch (err) {
    throw err;
  }
};

/** 刷新图片列表 */
const refreshPhotosList = async () => {
  try {
    const { list, ...res } = await useApi().getPhotosList({
      page: 1,
      pageSize: 20,
    });
    // 刷新时清空原有数据，重新加载
    imagesArray.value = list;
    paginated.value = res;

    useMessage({
      name: '刷新成功',
      description: '图片列表已刷新',
      type: 'success',
    });
  } catch (err) {
    // 显示刷新失败提示
    useMessage({
      name: '刷新失败',
      description: '网络连接异常，请稍后重试',
      type: 'error',
    });
    console.error('刷新图片列表失败:', err);
  }
};

/** 下拉刷新处理 */
const handleRefresh = async (done: () => void) => {
  try {
    await refreshPhotosList();
  } catch (err) {
    console.error('刷新失败:', err);
  } finally {
    // 完成刷新
    done();
  }
};

/** 加载图片列表 */
const handleLoadMore = async () => {
  if (loading.value || !hasMore.value) {
    return;
  }
  loading.value = true;
  await getPhotosList({ page: paginated.value.page + 1, pageSize: 20 });
  loading.value = false;
};

/** 初始化数据加载 */
const initializeData = async () => {
  loading.value = true;
  try {
    await getPhotosList({ page: 1, pageSize: 20 });
  } catch (err) {
    console.error('初始化数据加载失败:', err);
  } finally {
    loading.value = false;
  }
};

// 页面挂载时加载初始数据
onMounted(() => {
  initializeData();
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  > :last-child {
    flex: 1;
    overflow: auto;
  }

  .custom-item {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    > img {
      border-radius: 0.5rem;
      box-shadow: var(--shadow-neon-primary);
    }
  }
}
</style>

